# 问卷学科过滤功能演示

## 功能演示步骤

### 1. 获取学校课程列表

首先，管理员需要获取学校的所有课程，以便选择需要评价的课程。

```bash
# 获取学校课程列表
curl -X GET "http://localhost:3141/public/courses/school/school_001"
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取学校课程列表成功",
  "data": [
    {
      "id": "course_001",
      "code": "C001",
      "name": "语文",
      "sectionCode": "primary",
      "sectionName": "小学"
    },
    {
      "id": "course_002", 
      "code": "C002",
      "name": "数学",
      "sectionCode": "primary",
      "sectionName": "小学"
    },
    {
      "id": "course_003",
      "code": "C003", 
      "name": "英语",
      "sectionCode": "primary",
      "sectionName": "小学"
    }
  ]
}
```

### 2. 创建带课程过滤的问卷

管理员选择特定课程创建问卷：

```bash
# 创建问卷（需要JWT认证）
curl -X POST "http://localhost:3141/api/questionnaire" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "2024年1月教师评价问卷",
    "description": "请对本月教师表现进行评价",
    "month": "2024-01",
    "sso_school_code": "school_001",
    "star_mode": 5,
    "include_school_evaluation": true,
    "selected_courses": [
      {
        "sso_course_id": "course_001",
        "sso_course_code": "C001",
        "sso_course_name": "语文",
        "section_code": "primary",
        "section_name": "小学"
      },
      {
        "sso_course_id": "course_002",
        "sso_course_code": "C002", 
        "sso_course_name": "数学",
        "section_code": "primary",
        "section_name": "小学"
      }
    ]
  }'
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "问卷创建成功",
  "data": {
    "id": 1,
    "title": "2024年1月教师评价问卷",
    "month": "2024-01",
    "status": "draft",
    "sso_school_code": "school_001"
  }
}
```

### 3. 查看问卷关联的课程

```bash
# 获取问卷关联的课程列表
curl -X GET "http://localhost:3141/api/questionnaire/1/courses" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取问卷关联课程成功",
  "data": [
    {
      "sso_course_id": "course_001",
      "sso_course_code": "C001",
      "sso_course_name": "语文",
      "section_code": "primary",
      "section_name": "小学",
      "is_enabled": true
    },
    {
      "sso_course_id": "course_002",
      "sso_course_code": "C002",
      "sso_course_name": "数学", 
      "section_code": "primary",
      "section_name": "小学",
      "is_enabled": true
    }
  ]
}
```

### 4. 获取过滤后的教师列表

家长填写问卷时，系统会根据问卷配置的课程过滤教师：

```bash
# 获取过滤后的教师列表
curl -X GET "http://localhost:3141/api/questionnaire/1/filtered-teachers?enterpriseCode=school_001&gradeCode=1&classCode=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取过滤后教师列表成功",
  "data": [
    {
      "id": "teacher_001",
      "name": "张老师",
      "subject": "语文",
      "memberType": "语文教师",
      "employment_status": "在岗"
    },
    {
      "id": "teacher_002", 
      "name": "李老师",
      "subject": "数学",
      "memberType": "数学教师",
      "employment_status": "在岗"
    }
  ]
}
```

### 5. 更新问卷课程配置

管理员可以修改问卷的课程配置（仅限草稿状态）：

```bash
# 更新问卷课程配置
curl -X PUT "http://localhost:3141/api/questionnaire/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "selected_courses": [
      {
        "sso_course_id": "course_003",
        "sso_course_code": "C003",
        "sso_course_name": "英语",
        "section_code": "primary",
        "section_name": "小学"
      }
    ]
  }'
```

### 6. 创建无课程过滤的问卷

如果不传递 `selected_courses` 或传递空数组，则不进行课程过滤：

```bash
# 创建无课程过滤的问卷
curl -X POST "http://localhost:3141/api/questionnaire" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "2024年2月教师评价问卷（无过滤）",
    "description": "评价所有教师",
    "month": "2024-02",
    "sso_school_code": "school_001",
    "star_mode": 5,
    "include_school_evaluation": true
  }'
```

在这种情况下，获取教师列表时会返回班级的所有教师。

## 功能对比

### 有课程过滤的问卷
- 只显示教授指定课程的教师
- 减少家长评价负担
- 提高评价的针对性

### 无课程过滤的问卷
- 显示班级所有教师
- 保持原有功能不变
- 向下兼容现有问卷

## 数据库变化

新增了 `questionnaire_courses` 表来存储问卷与课程的关联关系：

```sql
SELECT * FROM questionnaire_courses WHERE questionnaire_id = 1;
```

**查询结果示例：**
```
+----+------------------+---------------+------------------+------------------+--------------+--------------+------------+---------------------+---------------------+
| id | questionnaire_id | sso_course_id | sso_course_code  | sso_course_name  | section_code | section_name | is_enabled | created_at          | updated_at          |
+----+------------------+---------------+------------------+------------------+--------------+--------------+------------+---------------------+---------------------+
|  1 |                1 | course_001    | C001             | 语文             | primary      | 小学         |          1 | 2024-01-15 10:00:00 | 2024-01-15 10:00:00 |
|  2 |                1 | course_002    | C002             | 数学             | primary      | 小学         |          1 | 2024-01-15 10:00:00 | 2024-01-15 10:00:00 |
+----+------------------+---------------+------------------+------------------+--------------+--------------+------------+---------------------+---------------------+
```

## 注意事项

1. **认证要求**：除了公开接口外，其他接口都需要JWT认证
2. **权限控制**：只有草稿状态的问卷才能修改课程配置
3. **数据一致性**：课程信息来源于SSO系统，需要确保数据同步
4. **容错处理**：如果过滤过程出错，会降级返回所有教师
5. **向下兼容**：现有问卷如果没有课程配置，会显示所有教师
