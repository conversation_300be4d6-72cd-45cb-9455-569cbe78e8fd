import { Provide, Inject } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize, QueryTypes, Transaction } from 'sequelize';
import { Op } from 'sequelize';
import { CustomError } from '../error/custom.error';
import {
  Questionnaire,
  QuestionnaireStatus,
} from '../entity/questionnaire.entity';
import { QuestionnaireCourse } from '../entity/questionnaire-course.entity';
import {
  CreateQuestionnaireDTO,
  UpdateQuestionnaireDTO,
  UpdateQuestionnaireStatusDTO,
  QueryQuestionnaireDTO,
  QuestionnaireCourseDTO,
} from '../dto/questionnaire.dto';
import { IQuestionnaireListResponse, ISSoSchoolInfo } from '../interface';
import { Custome } from './api_sso/custome.service';

@Provide()
export class QuestionnaireService {
  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectRepository(QuestionnaireCourse)
  questionnaireCourseRepository: Repository<QuestionnaireCourse>;

  @InjectDataSource()
  sequelize: Sequelize;

  @Inject()
  custome: Custome;

  /**
   * 验证SSO学校编码是否有效
   * @param ssoSchoolCode SSO学校编码
   * @returns 学校信息
   */
  private async validateSSOSchoolCode(
    ssoSchoolCode: string
  ): Promise<ISSoSchoolInfo> {
    try {
      // 调用SSO接口验证学校编码
      const schoolInfo = await this.custome.getEnterpriseByCode(ssoSchoolCode);
      console.log('schoolInfo: ', schoolInfo);

      if (!schoolInfo) {
        throw new CustomError('学校编码无效或学校状态异常');
      }

      return {
        code: schoolInfo.code,
        name: schoolInfo.name,
        status: 'active',
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('验证学校编码时发生错误：' + error.message);
    }
  }

  /**
   * 创建问卷
   * @param createDto 创建问卷DTO
   * @param creatorUserId 创建用户ID
   * @param creatorUserName 创建用户名称
   * @returns 创建的问卷
   */
  async createQuestionnaire(
    createDto: CreateQuestionnaireDTO,
    creatorUserId: string,
    creatorUserName?: string
  ): Promise<Questionnaire> {
    // 开启事务
    const transaction: Transaction = await this.sequelize.transaction();

    try {
      // 验证SSO学校编码
      const schoolInfo = await this.validateSSOSchoolCode(
        createDto.sso_school_code
      );

      // 检查同一学校同一月份是否已存在问卷
      const existingQuestionnaire = await this.questionnaireRepository.findOne({
        where: {
          sso_school_code: createDto.sso_school_code,
          month: createDto.month,
        },
        transaction,
      });

      if (existingQuestionnaire) {
        throw new CustomError(`该学校在${createDto.month}月份已存在问卷`);
      }

      // 验证时间范围
      if (createDto.start_time && createDto.end_time) {
        if (createDto.start_time >= createDto.end_time) {
          throw new CustomError('问卷开始时间必须早于结束时间');
        }
      }

      // 创建问卷数据（排除课程选择字段）
      const { selected_courses, ...questionnaireData } = createDto;
      const finalQuestionnaireData = {
        ...questionnaireData,
        sso_school_name: schoolInfo.name,
        creator_user_id: creatorUserId,
        creator_user_name: creatorUserName,
        status: QuestionnaireStatus.DRAFT,
      };

      // 创建问卷
      const questionnaire = await this.questionnaireRepository.create(
        finalQuestionnaireData,
        { transaction }
      );

      // 如果有选择的课程，创建问卷课程关联记录
      if (selected_courses && selected_courses.length > 0) {
        const courseData = selected_courses.map(course => ({
          questionnaire_id: questionnaire.id,
          sso_course_id: course.sso_course_id,
          sso_course_code: course.sso_course_code,
          sso_course_name: course.sso_course_name,
          section_code: course.section_code,
          section_name: course.section_name,
          is_enabled: true,
        }));

        await this.questionnaireCourseRepository.bulkCreate(courseData, {
          transaction,
        });
      }

      // 提交事务
      await transaction.commit();
      return questionnaire;
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 查询问卷列表
   * @param queryDto 查询条件DTO
   * @returns 问卷列表和分页信息
   */
  async getQuestionnaireList(
    queryDto: QueryQuestionnaireDTO
  ): Promise<IQuestionnaireListResponse> {
    const { sso_school_code, month, status, page = 1, limit = 10 } = queryDto;

    // 构建查询条件
    const where: any = {};
    if (sso_school_code) {
      where.sso_school_code = sso_school_code;
    }
    if (month) {
      where.month = month;
    }
    if (status) {
      where.status = status;
    }

    // 计算分页参数
    const offset = (page - 1) * limit;

    // 查询数据
    const { rows: list, count: total } =
      await this.questionnaireRepository.findAndCountAll({
        where,
        offset,
        limit,
        order: [['created_at', 'DESC']],
      });

    return {
      list,
      total,
      page,
      limit,
    };
  }

  /**
   * 根据ID获取问卷详情
   * @param id 问卷ID
   * @returns 问卷详情
   */
  async getQuestionnaireById(id: number): Promise<Questionnaire> {
    const questionnaire = await this.questionnaireRepository.findByPk(id);
    if (!questionnaire) {
      throw new CustomError('问卷不存在');
    }
    return questionnaire;
  }

  /**
   * 获取问卷关联的课程列表
   * @param questionnaireId 问卷ID
   * @returns 课程列表
   */
  async getQuestionnaireCourses(
    questionnaireId: number
  ): Promise<QuestionnaireCourseDTO[]> {
    const courses = await this.questionnaireCourseRepository.findAll({
      where: {
        questionnaire_id: questionnaireId,
        is_enabled: true,
      },
      order: [['sso_course_code', 'ASC']],
    });

    return courses.map(course => ({
      sso_course_id: course.sso_course_id,
      sso_course_code: course.sso_course_code,
      sso_course_name: course.sso_course_name,
      section_code: course.section_code,
      section_name: course.section_name,
      is_enabled: course.is_enabled,
    }));
  }

  /**
   * 更新问卷
   * @param id 问卷ID
   * @param updateDto 更新问卷DTO
   * @returns 更新后的问卷
   */
  async updateQuestionnaire(
    id: number,
    updateDto: UpdateQuestionnaireDTO
  ): Promise<Questionnaire> {
    // 开启事务
    const transaction: Transaction = await this.sequelize.transaction();

    try {
      const questionnaire = await this.getQuestionnaireById(id);

      // 只有草稿状态的问卷才能修改基本信息
      if ([QuestionnaireStatus.PUBLISHED].includes(questionnaire.status)) {
        throw new CustomError('已发布的问卷不能修改基本信息');
      }

      // 构建更新数据，只更新传入的字段（排除课程选择字段）
      const { selected_courses, ...updateFields } = updateDto;
      const updateData: Partial<Questionnaire> = {};

      if (updateFields.title !== undefined)
        updateData.title = updateFields.title;
      if (updateFields.description !== undefined)
        updateData.description = updateFields.description;
      if (updateFields.month !== undefined)
        updateData.month = updateFields.month;
      if (updateFields.star_mode !== undefined)
        updateData.star_mode = updateFields.star_mode;
      if (updateFields.include_school_evaluation !== undefined)
        updateData.include_school_evaluation =
          updateFields.include_school_evaluation;
      if (updateFields.instructions !== undefined)
        updateData.instructions = updateFields.instructions;
      if (updateFields.allow_anonymous !== undefined)
        updateData.allow_anonymous = updateFields.allow_anonymous;
      if (updateFields.max_teachers_limit !== undefined)
        updateData.max_teachers_limit = updateFields.max_teachers_limit;
      if (updateFields.start_time !== undefined)
        updateData.start_time = updateFields.start_time;
      if (updateFields.end_time !== undefined)
        updateData.end_time = updateFields.end_time;

      // 如果更新了学校编码，需要验证学校是否存在并获取学校信息
      if (
        updateFields.sso_school_code &&
        updateFields.sso_school_code !== questionnaire.sso_school_code
      ) {
        const schoolInfo = await this.validateSSOSchoolCode(
          updateFields.sso_school_code
        );
        updateData.sso_school_code = updateFields.sso_school_code;
        updateData.sso_school_name = schoolInfo.name;
      }

      // 执行问卷基本信息更新
      if (Object.keys(updateData).length > 0) {
        await this.questionnaireRepository.update(updateData, {
          where: { id },
          transaction,
        });
      }

      // 如果有课程选择更新，处理课程关联
      if (selected_courses !== undefined) {
        // 先删除现有的课程关联
        await this.questionnaireCourseRepository.destroy({
          where: { questionnaire_id: id },
          transaction,
        });

        // 如果有新的课程选择，创建新的关联记录
        if (selected_courses && selected_courses.length > 0) {
          const courseData = selected_courses.map(course => ({
            questionnaire_id: id,
            sso_course_id: course.sso_course_id,
            sso_course_code: course.sso_course_code,
            sso_course_name: course.sso_course_name,
            section_code: course.section_code,
            section_name: course.section_name,
            is_enabled: true,
          }));

          await this.questionnaireCourseRepository.bulkCreate(courseData, {
            transaction,
          });
        }
      }

      // 提交事务
      await transaction.commit();

      // 返回更新后的问卷
      return await this.getQuestionnaireById(id);
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 更新问卷状态
   * @param id 问卷ID
   * @param updateDto 更新状态DTO
   * @returns 更新后的问卷
   */
  async updateQuestionnaireStatus(
    id: number,
    updateDto: UpdateQuestionnaireStatusDTO
  ): Promise<Questionnaire> {
    const questionnaire = await this.getQuestionnaireById(id);

    // 验证状态转换是否合法
    this.validateStatusTransition(questionnaire.status, updateDto.status);

    // 如果要发布问卷，需要确保同一学校只能有一个发布状态的问卷
    if (updateDto.status === QuestionnaireStatus.PUBLISHED) {
      await this.ensureOnlyOnePublishedQuestionnaire(
        questionnaire.sso_school_code,
        id
      );
    }

    // 更新状态
    await this.questionnaireRepository.update(
      { status: updateDto.status },
      { where: { id } }
    );

    // 返回更新后的问卷
    return await this.getQuestionnaireById(id);
  }

  /**
   * 确保同一学校只能有一个发布状态的问卷
   * @param ssoSchoolCode 学校编码
   * @param currentQuestionnaireId 当前问卷ID（排除自己）
   */
  private async ensureOnlyOnePublishedQuestionnaire(
    ssoSchoolCode: string,
    currentQuestionnaireId: number
  ): Promise<void> {
    // 查找该学校是否已有其他发布状态的问卷
    const existingPublishedQuestionnaire =
      await this.questionnaireRepository.findOne({
        where: {
          sso_school_code: ssoSchoolCode,
          status: QuestionnaireStatus.PUBLISHED,
          id: { [Op.ne]: currentQuestionnaireId }, // 排除当前问卷
        },
      });

    if (existingPublishedQuestionnaire) {
      throw new CustomError(
        `该学校已有发布状态的问卷："${existingPublishedQuestionnaire.title}"，请先关闭该问卷后再发布新问卷`
      );
    }
  }

  /**
   * 验证问卷状态转换是否合法
   * @param currentStatus 当前状态
   * @param newStatus 新状态
   */
  private validateStatusTransition(
    currentStatus: QuestionnaireStatus,
    newStatus: QuestionnaireStatus
  ): void {
    const validTransitions: Record<QuestionnaireStatus, QuestionnaireStatus[]> =
      {
        [QuestionnaireStatus.DRAFT]: [
          QuestionnaireStatus.PUBLISHED,
          QuestionnaireStatus.CLOSED,
        ],
        [QuestionnaireStatus.PUBLISHED]: [QuestionnaireStatus.CLOSED],
        [QuestionnaireStatus.CLOSED]: [QuestionnaireStatus.PUBLISHED], // 已关闭的问卷可再次发布
      };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new CustomError(`不能从${currentStatus}状态转换到${newStatus}状态`);
    }
  }

  /**
   * 删除问卷（仅限草稿状态）
   * @param id 问卷ID
   */
  async deleteQuestionnaire(id: number): Promise<void> {
    const questionnaire = await this.getQuestionnaireById(id);

    // 只有草稿状态的问卷才能删除
    if (
      ![QuestionnaireStatus.DRAFT, QuestionnaireStatus.CLOSED].includes(
        questionnaire.status
      )
    ) {
      throw new CustomError('只有草稿状态的问卷才能删除');
    }

    // 开启事务，确保删除操作的原子性
    const transaction: Transaction = await this.sequelize.transaction();

    try {
      // 1. 先删除所有相关的答案记录
      await this.sequelize.query(
        `DELETE answers FROM answers
         INNER JOIN responses ON answers.response_id = responses.id
         WHERE responses.questionnaire_id = :questionnaireId`,
        {
          replacements: { questionnaireId: id },
          type: QueryTypes.DELETE,
          transaction,
        }
      );

      // 2. 删除所有相关的响应记录
      await this.sequelize.query(
        `DELETE FROM responses WHERE questionnaire_id = :questionnaireId`,
        {
          replacements: { questionnaireId: id },
          type: QueryTypes.DELETE,
          transaction,
        }
      );

      // 3. 删除问卷课程关联记录
      await this.sequelize.query(
        `DELETE FROM questionnaire_courses WHERE questionnaire_id = :questionnaireId`,
        {
          replacements: { questionnaireId: id },
          type: QueryTypes.DELETE,
          transaction,
        }
      );

      // 4. 最后删除问卷记录
      await this.questionnaireRepository.destroy({
        where: { id },
        transaction
      });

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 获取指定学校近1年内的问卷列表（公开接口）
   * @param ssoSchoolCode 学校编码
   * @returns 问卷列表
   */
  async getPublicQuestionnairesBySchool(
    ssoSchoolCode: string
  ): Promise<Questionnaire[]> {
    // 计算1年前的日期
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const oneYearAgoMonth = oneYearAgo.toISOString().slice(0, 7); // YYYY-MM格式

    // 查询指定学校近1年内已发布的问卷
    const questionnaires = await this.questionnaireRepository.findAll({
      where: {
        sso_school_code: ssoSchoolCode,
        status: QuestionnaireStatus.PUBLISHED,
        month: {
          [Op.gte]: oneYearAgoMonth,
        },
      },
      attributes: [
        'id',
        'title',
        'description',
        'month',
        'star_mode',
        'sso_school_code',
        'sso_school_name',
        'start_time',
        'end_time',
        'created_at',
      ],
      order: [
        ['month', 'DESC'],
        ['created_at', 'DESC'],
      ],
    });

    return questionnaires;
  }

  /**
   * 获取指定问卷的教师成绩统计（公开接口）
   * @param questionnaireId 问卷ID
   * @returns 教师成绩列表
   */
  async getPublicTeacherScoresByQuestionnaire(
    questionnaireId: number
  ): Promise<any[]> {
    // 首先验证问卷是否存在且已发布
    const questionnaire = await this.questionnaireRepository.findOne({
      where: {
        id: questionnaireId,
        status: QuestionnaireStatus.PUBLISHED,
      },
    });

    if (!questionnaire) {
      throw new CustomError('问卷不存在或未发布');
    }

    // 使用原生SQL查询教师成绩统计
    const teacherScores = await this.sequelize.query(
      `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      WHERE r.questionnaire_id = :questionnaireId
        AND r.is_completed = 1
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
      ORDER BY average_score DESC, evaluation_count DESC
    `,
      {
        replacements: { questionnaireId },
        type: QueryTypes.SELECT,
      }
    );

    return teacherScores.map((item: any) => ({
      sso_teacher_id: item.sso_teacher_id,
      sso_teacher_name: item.sso_teacher_name,
      sso_teacher_subject: item.sso_teacher_subject,
      sso_teacher_department: item.sso_teacher_department,
      evaluation_count: parseInt(item.evaluation_count) || 0,
      average_score: parseFloat(item.average_score) || 0,
    }));
  }
}
