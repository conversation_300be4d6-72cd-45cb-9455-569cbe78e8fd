/*
 * @Description: 对接客户系统相关接口
 * @Date: 2025-03-29 10:17:13
 * @LastEditors: ZhuPengliang <EMAIL>
 * @LastEditTime: 2025-06-01 04:00:22
 */
import { Inject, Provide } from '@midwayjs/core';
import { APIManager } from './index.service';
import { IUserInfo, ParentInfo } from './interface';

@Provide()
export class Custome {
  @Inject()
  apiManager: APIManager;

  /**
   * 根据学校编号查询学校信息
   * @param enterpriseCode 学校编号
   * @returns 学校信息
   */
  async getEnterpriseByCode(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getEnterpriseByCode',
      params: enterpriseCode,
    });
    return result;
  }

  /**
   * 查询学期列表
   * @param enterpriseCode 学校编号
   * @returns 学期列表
   */
  async getSemesterList(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSemesterList',
      params: enterpriseCode,
    });
    return result;
  }

  /**
   * 查询角色列表
   * @param enterpriseCode 学校编号
   * @returns 角色列表
   * */
  async getSchoolRoles(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSchoolRoles',
      query: { enterpriseCode },
    });
    return { list: result.list };
  }

  /**
   * 查询角色下的用户列表
   * @param enterpriseCode 学校编号
   * @param roleCode 角色编号
   * @param limit 每页数量
   * @param offset 页码
   * @returns 用户列表
   * */
  async getSchoolUserRoleList(
    enterpriseCode: string,
    roleCode: string,
    limit?: number,
    offset?: number
  ): Promise<{ count: number; list: IUserInfo[] }> {
    const result = await this.apiManager.send({
      apiCode: 'getSchoolUserRoleList',
      query: { enterpriseCode, roleCode, limit, offset },
    });
    return result;
  }

  /**
   * 获取指定学校下的所有成员
   *
   * @param {string} enterpriseCode 学校编号
   * @return {*} list 成员列表
   * @memberof Custome
   */
  async getMembers(
    enterpriseCode: string
  ): Promise<{ count: number; list: IUserInfo[] }> {
    const result = await this.apiManager.send({
      apiCode: 'getMembers',
      query: { enterpriseCode },
    });
    return result;
  }

  /**
   * 获取班级教师通讯录
   * @param enterpriseCode 学校编号
   * @param gradeCode 年级编号
   * @param classCode 班级编号
   * @returns 教师列表
   */
  async getTeacherListForClass(
    enterpriseCode: string,
    gradeCode: string,
    classCode: string
  ) {
    const result = await this.apiManager.send({
      apiCode: 'getTeacherListForClass',
      params: enterpriseCode,
      query: { gradeCode, classCode },
    });
    return result;
  }

  /**
   * 根据用户编号获取用户信息
   *
   * @param {string} enterpriseCode 学校编号
   * @param {string} code 用户编号
   * @return {*} 用户信息
   * @memberof Custome
   */
  async getMemberByCode(enterpriseCode: string, code: string) {
    const result = await this.apiManager.send({
      apiCode: 'getMemberByCode',
      params: `${enterpriseCode}/${code}`,
    });
    return result;
  }

  /**
   * 获取指定家长手机号下的所有学生信息
   *
   * @param {string} mobile 家长手机号
   * @return {*} list 学生列表
   * @memberof Custome
   */
  async getChildren(mobile: string): Promise<ParentInfo> {
    const result = await this.apiManager.send({
      apiCode: 'getChildren',
      query: { mobile },
    });
    return result;
  }

  /**
   * 获取指定学校的指定学生信息
   *
   * @param {string} enterpriseId 学校id，注意目前custome没有提供通过学校编号查询学生接口
   * @param {string} code 学生编号
   * @param {string} name 学生姓名
   * @return {*} 学生信息
   * @memberof Custome
   */
  async getStudentInfo(enterpriseId: string, code: string, name: string) {
    const result = await this.apiManager.send({
      apiCode: 'getStudent',
      query: { enterpriseId, code, name },
    });
    return result;
  }
}
