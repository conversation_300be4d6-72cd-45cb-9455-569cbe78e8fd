import {
  Controller,
  Post,
  Get,
  Put,
  Del,
  Inject,
  Body,
  Param,
  Query,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { QuestionnaireService } from '../service/questionnaire.service';
import { ResponseService } from '../service/response.service';
import {
  CreateQuestionnaireDTO,
  UpdateQuestionnaireDTO,
  UpdateQuestionnaireStatusDTO,
  QueryQuestionnaireDTO,
} from '../dto/questionnaire.dto';
import { BaseController } from '../common/BaseController';
import { ApiResponseUtil } from '../common/ApiResponse';
import { OperationType } from '../entity/operation-log.entity';

@Controller('/api/questionnaire')
export class QuestionnaireController extends BaseController {
  @Inject()
  questionnaireService: QuestionnaireService;

  @Inject()
  responseService: ResponseService;

  /**
   * 创建问卷
   */
  @Post('/')
  @Validate()
  async createQuestionnaire(@Body() createDto: CreateQuestionnaireDTO) {
    const { userId, userName } = this.getCurrentUser();

    const questionnaire = await this.questionnaireService.createQuestionnaire(
      createDto,
      userId,
      userName
    );

    await this.logOperation(
      '创建问卷',
      { questionnaireId: questionnaire.id },
      undefined,
      OperationType.CREATE
    );
    return ApiResponseUtil.success(questionnaire, '问卷创建成功');
  }

  /**
   * 获取问卷列表
   */
  @Get('/')
  @Validate()
  async getQuestionnaireList(@Query() queryDto: QueryQuestionnaireDTO) {
    const result = await this.questionnaireService.getQuestionnaireList(
      queryDto
    );
    return ApiResponseUtil.success(result, '获取问卷列表成功');
  }

  /**
   * 更新问卷状态
   */
  @Put('/:id/status')
  @Validate()
  async updateQuestionnaireStatus(
    @Param('id') id: number,
    @Body() updateDto: UpdateQuestionnaireStatusDTO
  ) {
    const questionnaire =
      await this.questionnaireService.updateQuestionnaireStatus(id, updateDto);

    await this.logOperation(
      '更新问卷状态',
      {
        questionnaireId: id,
        status: updateDto.status,
      },
      undefined,
      updateDto.status === 'published'
        ? OperationType.PUBLISH
        : OperationType.UPDATE
    );
    return ApiResponseUtil.success(questionnaire, '问卷状态更新成功');
  }

  /**
   * 获取问卷统计信息
   */
  @Get('/:id/statistics')
  async getQuestionnaireStatistics(@Param('id') id: number) {
    // 验证问卷是否存在
    await this.questionnaireService.getQuestionnaireById(id);

    // TODO: 实现统计逻辑
    const statistics = {
      questionnaire_id: id,
      total_responses: 0,
      completed_responses: 0,
      completion_rate: 0,
      average_rating: 0,
    };

    return ApiResponseUtil.success(statistics, '获取问卷统计信息成功');
  }

  /**
   * 获取问卷关联的课程列表
   */
  @Get('/:id/courses')
  async getQuestionnaireCourses(@Param('id') id: number) {
    // 验证问卷是否存在
    await this.questionnaireService.getQuestionnaireById(id);

    const courses = await this.questionnaireService.getQuestionnaireCourses(id);
    return ApiResponseUtil.success(courses, '获取问卷关联课程成功');
  }

  /**
   * 获取问卷过滤后的教师列表
   * 根据问卷配置的课程过滤教师
   */
  @Get('/:id/filtered-teachers')
  async getFilteredTeachers(
    @Param('id') questionnaireId: number,
    @Query('enterpriseCode') enterpriseCode: string,
    @Query('gradeCode') gradeCode: string,
    @Query('classCode') classCode: string
  ) {
    // 验证问卷是否存在
    const questionnaire = await this.questionnaireService.getQuestionnaireById(questionnaireId);

    // 验证必要参数
    if (!enterpriseCode || !gradeCode || !classCode) {
      return ApiResponseUtil.error(400, '学校编码、年级编码和班级编码都是必填参数');
    }

    // 获取问卷关联的课程信息（用于调试）
    const questionnaireCourses = await this.questionnaireService.getQuestionnaireCourses(questionnaireId);
    console.log(`问卷 ${questionnaireId} 的课程配置:`, questionnaireCourses);

    const teachers = await this.responseService.getFilteredTeacherList(
      questionnaireId,
      enterpriseCode,
      gradeCode,
      classCode
    );

    return ApiResponseUtil.success({
      questionnaire_info: {
        id: questionnaire.id,
        title: questionnaire.title,
        course_count: questionnaireCourses.length,
        courses: questionnaireCourses
      },
      teachers: teachers,
      teacher_count: teachers.length
    }, '获取过滤后教师列表成功');
  }

  /**
   * 根据ID获取问卷详情
   */
  @Get('/:id')
  async getQuestionnaireById(@Param('id') id: number) {
    const questionnaire = await this.questionnaireService.getQuestionnaireById(
      id
    );
    return ApiResponseUtil.success(questionnaire, '获取问卷详情成功');
  }

  /**
   * 更新问卷
   */
  @Put('/:id')
  @Validate()
  async updateQuestionnaire(
    @Param('id') id: number,
    @Body() updateDto: UpdateQuestionnaireDTO
  ) {
    const questionnaire = await this.questionnaireService.updateQuestionnaire(
      id,
      updateDto
    );

    await this.logOperation(
      '更新问卷',
      {
        questionnaireId: id,
        updateFields: Object.keys(updateDto),
      },
      undefined,
      OperationType.UPDATE
    );
    return ApiResponseUtil.success(questionnaire, '问卷更新成功');
  }

  /**
   * 删除问卷（仅限草稿状态）
   */
  @Del('/:id')
  async deleteQuestionnaire(@Param('id') id: number) {
    await this.questionnaireService.deleteQuestionnaire(id);

    await this.logOperation(
      '删除问卷',
      { questionnaireId: id },
      undefined,
      OperationType.DELETE
    );
    return ApiResponseUtil.success(null, '问卷删除成功');
  }
}
